-- variables.lua - Simplified shared variables for Air780EG
local variables = {}

-- Basic states
variables.relay1_state = 0
variables.relay2_state = 0
variables.key_state = false
variables.sound_flag = true
variables.gps_flag = true
variables.carAlreadyStarted = false
variables.isLicensed = true

-- Communication
variables.sms_data = nil
variables.callback_number = nil
variables.phone_number1 = "89932933"  -- Your phone number (last 8 digits)
variables.phone_number2 = nil
variables.phone_number3 = nil

-- Timing
variables.currentTime = 0
variables.timers_queue = {}

-- Settings
variables.voltage_offset = 0
variables.voltage_threshold = 0.5
variables.voltage_notify_flag = false
variables.geely_atlas_mode = false
variables.last_mirror_action = "none"  -- Track last mirror action for Geely Atlas mode
variables.mirror_duration = 2000  -- Default mirror duration for Geely Atlas lock action

-- Auto-shutdown feature settings
variables.auto_shutdown_enabled = true  -- Enable auto-shutdown feature by default
variables.auto_shutdown_timer_id = nil  -- Timer ID for auto-shutdown
variables.auto_shutdown_minutes = 30  -- Default: 30 minutes (user-configurable)
variables.auto_shutdown_time = variables.auto_shutdown_minutes * 60 * 1000  -- Convert minutes to milliseconds
variables.last_as_command_time = 0  -- Timestamp of the last 'as' command
variables.as_wait_s2_falling = true

-- GPS Tracking feature settings
variables.tracking_enabled = false  -- GPS tracking auto-publish disabled by default
variables.tracking_timer_id = nil  -- Timer ID for GPS tracking
variables.tracking_interval_minutes = 5  -- Default: 5 minutes (user-configurable)
variables.last_tracking_publish_time = 0  -- Timestamp of the last tracking publish

-- Timing parameters (ms)
variables.lock_init_duration = 2000
variables.lock_press_duration = 1000
variables.unlock_press_duration = 1000
variables.lock_wait_duration = 2000
variables.unlock_wait_duration = 1000
variables.between_press_duration = 1000
variables.remote_start_duration = 4000
variables.relay1_on_duration = 3000
variables.relay2_on_duration = 3000
variables.relay3_on_duration = 3000

return variables
