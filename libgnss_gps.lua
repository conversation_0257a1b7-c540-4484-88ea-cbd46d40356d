local sys = require("sys")

-- GPS module is built-in for Air780EG - no require needed
-- libgnss and pm are global modules available in LuatOS

local GPS = {
    uart_id = 2,
    baudrate = 115200,
    satellites = 0,
    fix_quality = 0,
    hdop = 0,
    initialized = false,
    position = nil,
    last_fix_time = 0,
    fix_lost_count = 0,
    power_cycle_count = 0,
    cold_start_performed = false,
    last_power_cycle = 0,
    initialization_attempts = 0
}

-- Enhanced power cycle GPS module with proper timing
local function powerCycleGPS()
    local current_time = os.time()

    -- Prevent too frequent power cycles (minimum 30 seconds apart)
    if current_time - GPS.last_power_cycle < 30 then
        print("GPS power cycle too recent, skipping")
        return false
    end

    GPS.power_cycle_count = GPS.power_cycle_count + 1
    GPS.last_power_cycle = current_time

    print("GPS power cycling (attempt " .. GPS.power_cycle_count .. ")")

    -- Proper power cycle sequence
    pm.power(pm.GPS, false)
    sys.wait(3000)  -- Longer wait for complete power down

    pm.power(pm.GPS, true)
    sys.wait(2000)  -- Longer wait for power stabilization

    -- Clear and reinitialize UART/GNSS
    libgnss.clear()
    sys.wait(500)
    uart.setup(2, 115200)
    sys.wait(500)
    libgnss.bind(2)

    return true
end

-- Perform GPS cold start for faster acquisition
local function performColdStart()
    if GPS.cold_start_performed then return end

    print("Performing GPS cold start")

    -- Send cold start command to GPS module
    if uart and uart.write then
        -- Standard NMEA cold start command
        uart.write(2, "$PMTK104*37\r\n")  -- Cold start
        sys.wait(1000)
        uart.write(2, "$PMTK314,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0*28\r\n")  -- Set NMEA output
        sys.wait(500)
    end

    GPS.cold_start_performed = true
end

-- Initialize GPS using official Air780EG method
function GPS.init()
    GPS.initialization_attempts = GPS.initialization_attempts + 1
    print("GPS initialization attempt " .. GPS.initialization_attempts)

    -- Follow official Air780EG GPS initialization sequence
    sys.taskInit(function()
        print("GPS: Starting initialization task")

        -- Turn on GPS power (pm is global in LuatOS)
        pm.power(pm.GPS, true)
        sys.wait(100)

        -- Setup UART for GPS communication
        uart.setup(GPS.uart_id, GPS.baudrate)
        sys.wait(100)

        -- Bind libgnss to UART (libgnss is global in LuatOS)
        libgnss.bind(GPS.uart_id)
        sys.wait(100)

        -- Enable debug for development
        libgnss.debug(true)

        GPS.initialized = true
        print("GPS: Initialization complete")
    end)

    return true
end

-- Convert NMEA coordinate format to decimal degrees
local function convertCoordinate(coord)
    if not coord or coord == 0 then return 0 end

    -- NMEA format: DDMM.MMMM (degrees + minutes)
    local degrees = math.floor(coord / 100)
    local minutes = coord - (degrees * 100)

    return degrees + (minutes / 60)
end

-- Get current position using Air780EG method with enhanced error handling
function GPS.getPosition()
    if not GPS.initialized then
        print("GPS not initialized")
        return nil
    end

    -- Check if GPS module is responsive (only if libgnss is available)
    if libgnss and libgnss.getGga then
        local gga = libgnss.getGga()
        if gga then
            GPS.satellites = gga.satellites or 0
            GPS.hdop = gga.hdop or 0
            GPS.fix_quality = gga.quality or 0
        else
            -- GPS module might be unresponsive
            local current_time = os.time()
            if current_time - GPS.last_fix_time > 300 then  -- 5 minutes without any data
                print("GPS unresponsive for 5 minutes, power cycling...")
                sys.taskInit(function()
                    powerCycleGPS()
                    sys.wait(2000)
                    performColdStart()
                end)
            end
        end

        if libgnss.isFix() then
            local rmcData = libgnss.getRmc() or libgnss.getRmc(1)
            if rmcData and rmcData.lng and rmcData.lat then
                local lat_decimal, lng_decimal

                -- Enhanced coordinate validation and conversion
                if rmcData.lat > 0 and rmcData.lat < 90 and rmcData.lng > 0 and rmcData.lng < 180 then
                    lat_decimal, lng_decimal = rmcData.lat, rmcData.lng
                else
                    lat_decimal, lng_decimal = convertCoordinate(rmcData.lat), convertCoordinate(rmcData.lng)
                end

                -- Validate coordinates are reasonable
                if lat_decimal == 0 or lng_decimal == 0 or
                   lat_decimal > 90 or lat_decimal < -90 or
                   lng_decimal > 180 or lng_decimal < -180 then
                    print("Invalid GPS coordinates:", lat_decimal, lng_decimal)
                    return nil
                end

                local speed_raw = rmcData.speed and rmcData.speed * 1.852 or 0
                local speed_kmh = speed_raw > 5.0 and speed_raw or 0

                GPS.position = {
                    latitude = lat_decimal,
                    longitude = lng_decimal,
                    speed = speed_kmh,
                    satellites = GPS.satellites,
                    fix_quality = GPS.fix_quality,
                    hdop = GPS.hdop,
                    timestamp = os.time(),
                    source = "GPS",
                    lat = lat_decimal,  -- Add legacy format for compatibility
                    lng = lng_decimal
                }

                GPS.last_fix_time = os.time()
                return GPS.position
            end
        else
            GPS.fix_lost_count = GPS.fix_lost_count + 1
            GPS.position = nil
        end
    else
        -- libgnss not available, return basic GPS info
        print("GPS: libgnss not available, returning basic info")
        return {
            latitude = 0,
            longitude = 0,
            speed = 0,
            satellites = 0,
            fix_quality = 0,
            hdop = 0,
            timestamp = os.time(),
            source = "GPS_BASIC"
        }
    end
    return nil
end



-- Get satellite information
function GPS.getSatelliteInfo()
    local gga = libgnss and libgnss.getGga and libgnss.getGga()
    if gga then
        GPS.satellites = gga.satellites or 0
        GPS.hdop = gga.hdop or 0
        GPS.fix_quality = gga.quality or 0
    end
    return {
        satellites = GPS.satellites,
        fix_quality = GPS.fix_quality,
        hdop = GPS.hdop,
        has_fix = libgnss and libgnss.isFix() or false
    }
end

function GPS.hasFix()
    return libgnss and libgnss.isFix() or false
end

return GPS
