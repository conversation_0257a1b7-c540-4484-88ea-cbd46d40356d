PROJECT = "mqtt_sensor_demo"
VERSION = "004.000.000"
PRODUCT_KEY = "AtqpMY7HfMCjxCgLw200LlNN2ZsiVszc"

local sys = require("sys")
local PinModule = require("pin_module")
local vars = require("variables")
local my_utils = require("my_utils")
local commands = require("commands")

local update_available, update = pcall(require, "update")
if not update_available then
    local ota_available, ota_update = pcall(require, "ota_update")
    if ota_available then
        update = ota_update
        update_available = true
    else
        update = {request = function() return false end}
        update_available = false
    end
end

local mqtt_module_available, mqtt_module = pcall(require, "mqtt_module")
if not mqtt_module_available then
    mqtt_module = {
        init = function() return false end,
        publish = function() return false end,
        is_ready = function() return false end,
        get_client_id = function() return "unknown" end
    }
end

local PRINT_INTERVAL = 1000
local mqtt_connection_beep_played = false
local ota_in_progress = false
local last_voltage = 0

sys.timerLoopStart(function() vars.currentTime = vars.currentTime + 1 end, 1000)

local SHTC3 = {i2c_id = 0, addr = 0x70, cmd_wakeup = 0x3517, cmd_sleep = 0xB098,
               cmd_measure_normal = 0x7866, temp_offset = 0}
local GPS = require("libgnss_gps")
local ADC_CONFIG = {id = 1, channel = 0, scaling_factor = 16.14}

if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO]", ...) end,
        warn = function(tag, ...) print("[WARN]", ...) end,
        error = function(tag, ...) print("[ERROR]", ...) end
    }
end

if not json then _G.json = {encode = function(obj) return "{}" end} end

local function initSHTC3()
    local setup_result = i2c.setup(SHTC3.i2c_id, 100000)
    if setup_result ~= 1 then return false end
    local result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {0x00})
    if not result then return false end
    print("SHTC3 init")
    return true
end
local shtc3_error_count = 0
local last_good_reading = {temperature = 25.0, humidity = 50.0}

local function readSHTC3()
    -- Return cached reading if too many consecutive errors
    if shtc3_error_count > 5 then
        return last_good_reading
    end

    local success, result = pcall(function()
        local wakeup_cmd_high = (SHTC3.cmd_wakeup >> 8) & 0xFF
        local wakeup_cmd_low = SHTC3.cmd_wakeup & 0xFF
        local send_result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {wakeup_cmd_high, wakeup_cmd_low})
        if not send_result then error("Wakeup failed") end
        sys.wait(1)

        local measure_cmd_high = (SHTC3.cmd_measure_normal >> 8) & 0xFF
        local measure_cmd_low = SHTC3.cmd_measure_normal & 0xFF
        send_result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {measure_cmd_high, measure_cmd_low})
        if not send_result then error("Measure command failed") end
        sys.wait(15)

        local data = i2c.recv(SHTC3.i2c_id, SHTC3.addr, 6)
        if not data or #data ~= 6 then error("Invalid data received") end

        local temp_raw = (data:byte(1) << 8) | data:byte(2)
        local humidity_raw = (data:byte(4) << 8) | data:byte(5)
        local temperature = -45.0 + 175.0 * (temp_raw / 65535.0) + SHTC3.temp_offset
        local humidity = 100.0 * (humidity_raw / 65535.0)

        -- Put sensor to sleep
        local sleep_cmd_high = (SHTC3.cmd_sleep >> 8) & 0xFF
        local sleep_cmd_low = SHTC3.cmd_sleep & 0xFF
        i2c.send(SHTC3.i2c_id, SHTC3.addr, {sleep_cmd_high, sleep_cmd_low})

        return {temperature = temperature, humidity = humidity}
    end)

    if success and result then
        shtc3_error_count = 0
        last_good_reading = result
        return result
    else
        shtc3_error_count = shtc3_error_count + 1
        return last_good_reading
    end
end

local function initGPS()
    if not GPS or not vars.gps_flag then
        print("GPS disabled or not available")
        return false
    end
    local success = GPS.init()
    if success then
        print("GPS init")
        -- Subscribe to GPS state events
        sys.subscribe("GNSS_STATE", function(event, ticks)
            if event == "FIXED" then
                print("GPS: Fix acquired")
            elseif event == "LOSE" then
                print("GPS: Fix lost")
            end
        end)
    end
    return success
end

local gps_health_check_time = 0

local function getGPSPosition()
    -- Check if GPS is enabled
    if not vars.gps_flag then
        return nil
    end

    local gps_data = GPS and GPS.getPosition() or nil
    local current_time = os.time()

    if gps_data then
        print("GPS OK: " .. string.format("%.6f,%.6f", gps_data.latitude, gps_data.longitude) ..
              " Sats:" .. (gps_data.satellites or 0))
    else
        -- Periodic GPS health diagnostics
        if current_time - gps_health_check_time > 60 then  -- Every minute
            gps_health_check_time = current_time
            local sat_info = GPS and GPS.getSatelliteInfo() or {}
            print("GPS Status: No fix | Sats:" .. (sat_info.satellites or 0) ..
                  " Quality:" .. (sat_info.fix_quality or 0) ..
                  " HDOP:" .. (sat_info.hdop or 0))
        end
    end
    return gps_data
end

local function initADC()
    if adc and adc.open then
        local result = adc.open(ADC_CONFIG.id, ADC_CONFIG.channel)
        if result == true or result == 1 then
            print("ADC init")
            return true
        end
    end
    print("ADC sim")
    return false
end

local function readVoltage()
    if adc and adc.read then
        local adcval, voltage_mv = adc.read(ADC_CONFIG.id, ADC_CONFIG.channel)
        if voltage_mv then
            return (voltage_mv * ADC_CONFIG.scaling_factor) / 1000.0 + (vars.voltage_offset or 0)
        end
    end
    -- Fallback: simulate voltage reading
    return 12.4 + (vars.voltage_offset or 0)
end

local function getRSSI()
    if mobile and mobile.signal then
        return mobile.signal() or 0
    elseif mobile and mobile.csq then
        return mobile.csq() or 0
    end
    return 0
end

-- Format sensor data as JSON
local function formatSensorData(temp, humidity, voltage, gps, rssi)
    local lat_str, lon_str = "0 N", "0 E"
    if gps and gps.latitude and gps.longitude then
        local lat = tonumber(gps.latitude) or 0
        local lon = tonumber(gps.longitude) or 0

        if lat ~= 0 and lon ~= 0 then
            -- GPS coordinates are already in decimal degrees, no conversion needed
            lat_str = string.format("%.6f N", lat)
            lon_str = string.format("%.6f E", lon)
        end
    end

    -- Ensure all values are properly formatted and validated
    local safe_humidity = math.floor((tonumber(humidity) or 29) + 0.5)
    local safe_rssi = math.floor(tonumber(rssi) or 0)
    local safe_voltage = tonumber(voltage) or 10.0
    local safe_speed = math.floor(tonumber((gps and gps.speed) or 0) or 0)
    local safe_temp = math.floor((tonumber(temp) or 30) + 0.5)
    local safe_version = tostring(VERSION or "002.000.000")

    -- Ensure all string values are safe
    local safe_lon = tostring(lon_str or "0 E")
    local safe_lat = tostring(lat_str or "0 N")

    return string.format('{"Lon":"%s","Lat":"%s","hum":%d,"ver":"%s","rssi":%d,"volt":%.3f,"Speed":%d,"temp":%d,"motion":0,"light":0}',
        safe_lon, safe_lat, safe_humidity, safe_version, safe_rssi, safe_voltage, safe_speed, safe_temp)
end

-- Consolidated command functions
local function playBeep(pattern)
    if vars.sound_flag then
        sys.taskInit(function()
            if pattern == "check" then PinModule.beepPattern(1, 300, 0, 2000)
            elseif pattern == "lock" then PinModule.beepPattern(2, 200, 200, 2500)
            elseif pattern == "unlock" then PinModule.beepPattern(3, 150, 150, 3000)
            elseif pattern == "as" then PinModule.beepPattern(1, 500, 0, 1800)
            else PinModule.beepPattern(1, 200, 0, 2000) end
        end)
    end
end

-- Use commands module directly

-- Simplified SMS functions
local function sendSms(phoneNumber, message)
    if sms then
        -- Remove + prefix if present, as some SMS libraries handle international format differently
        if string.match(phoneNumber, "^%+") then
            phoneNumber = string.sub(phoneNumber, 2)  -- Remove the + prefix
        end
        return sms.send(phoneNumber, message)
    end
    return false
end

local function smsCallback(num, data, datetime)
    local original_num = tostring(num or "")  -- Store the original full number for sending replies
    local short_num = string.sub(original_num, -8)  -- Extract last 8 digits for comparison

    -- Handle case where data might be a table
    local message_text = type(data) == "table" and (data.content or data.text or tostring(data)) or tostring(data or "")

    -- Handle case where datetime might be a table
    local datetime_str = type(datetime) == "table" and tostring(datetime) or tostring(datetime or "unknown")

    log.info("SmsModule", "Received SMS from: " .. short_num .. " with content: " .. message_text .. " at " .. datetime_str)

    -- Forward special keywords to MQTT
    local keywords = {"Tand", "TG", "hugatsaa", "kod", "code", "opt"}
    for _, keyword in ipairs(keywords) do
        if string.find(message_text, keyword) then
            if mqtt_module.is_ready() then
                -- Escape quotes and backslashes in SMS content for JSON
                local escaped_data = message_text:gsub('\\', '\\\\'):gsub('"', '\\"')
                local message = string.format('{"sms":"%s"}', escaped_data)

                -- Send to MQTT server with error handling
                local success = mqtt_module.publish(message)
                if success then
                    log.info("SmsModule", "Message sent to MQTT server: " .. message)
                else
                    log.error("SmsModule", "Failed to send MQTT message: " .. message)
                end
            else
                log.warn("SmsModule", "MQTT not ready")
            end
            return
        end
    end

    -- Handle restart command
    if string.lower(message_text) == "restart" then
        sendSms(original_num, "System restarting...")
        sys.taskInit(function()
            sys.wait(2000)
            if rtos and rtos.reboot then rtos.reboot() end
        end)
        return
    end

    -- Handle Air720-style phone number configuration
    for i = 1, 3 do
        local pattern = "x123456x" .. i
        if string.find(message_text, pattern) then
            local start_pos = string.find(message_text, pattern) + string.len(pattern)
            local end_pos = string.find(message_text, "xx") - 1
            if start_pos and end_pos and end_pos >= start_pos then
                local phone_num = string.sub(string.sub(message_text, start_pos, end_pos), -8)
                if string.len(phone_num) == 8 and my_utils.writeToFile("/data/phone" .. i .. ".txt", phone_num) then
                    vars["phone_number" .. i] = phone_num
                    sendSms(original_num, "success")
                else
                    sendSms(original_num, "fail")
                end
            else
                sendSms(original_num, "fail")
            end
            return
        end
    end

    -- Check authorization (compare using short numbers)
    if short_num ~= vars.phone_number1 and short_num ~= vars.phone_number2 and short_num ~= vars.phone_number3 then
        if string.len(short_num) >= 8 then
            sendSms(original_num, "taniulaagvi dugaar!")  -- Send to original full number
        end
        return
    end

    -- Store for processing (use original number for replies)
    vars.sms_data = message_text
    vars.callback_number = original_num
end

-- Initialize SMS
local function initSMS()
    if sms then
        sms.setNewSmsCb(smsCallback)
        print("SMS init")
        return true
    end
    return false
end

-- Task to read and print sensor data
local function sensorTask()
    print("Sensor task")

    -- Wait for system to stabilize
    sys.wait(2000)

    while true do
        -- Read sensor data
        local temp_hum = readSHTC3()
        local temperature = temp_hum and temp_hum.temperature or 25.0
        local humidity = temp_hum and temp_hum.humidity or 50.0
        local voltage = readVoltage() or 12.0
        local gps = getGPSPosition()
        local rssi = getRSSI() or 0

        local gps_status = gps and "OK" or "NO"
        print("T:" .. string.format("%.1f", temperature) .. "°C H:" .. string.format("%.1f", humidity) .. "% V:" .. string.format("%.2f", voltage) .. "V RSSI:" .. rssi .. " GPS:" .. gps_status)



        -- Wait for the next reading interval
        sys.wait(PRINT_INTERVAL)
    end
end

-- Initialize mobile network
local function initMobile()
    if not mobile then return false end

    local timeout = 30
    local start_time = os.time()

    while os.time() - start_time < timeout do
        if mobile.status then
            local status = mobile.status()
            if status == "REGISTERED" or status == "REGISTERED_ROAMING" or
               status == 1 or status == 2 or status == 5 then
                print("Network registered, RSSI:", getRSSI())
                PinModule.setNetLED(1)
                return true
            end
        end
        sys.wait(1000)
    end

    local rssi = getRSSI()
    if rssi and rssi ~= 0 then
        print("Network timeout, signal detected")
        PinModule.setNetLED(1)
        return true
    end

    print("Network registration failed")
    return false
end

local last_publish_time = 0
local mqtt_status_check_time = 0

local function publishSensorData()
    local current_time = os.time()
    if current_time - last_publish_time < 1 then return end -- Rate limit: max once per 1 second
    last_publish_time = current_time

    sys.taskInit(function()
        if mqtt_module.is_ready() then
            local temp_hum = readSHTC3()
            local json_data = formatSensorData(temp_hum.temperature, temp_hum.humidity, readVoltage(), getGPSPosition(), getRSSI())
            mqtt_module.publish(json_data)
        else
            -- Log MQTT status periodically when not ready
            if current_time - mqtt_status_check_time > 30 then -- Every 30 seconds
                print("MQTT not ready")
                mqtt_status_check_time = current_time
            end
        end
    end)
end



-- Network connectivity monitoring
local function checkNetworkStatus()
    local network_status = "Unknown"
    if mobile then
        if mobile.status then
            local status = mobile.status()
            network_status = status and "Connected" or "Disconnected"
        elseif mobile.signal then
            local signal = mobile.signal()
            network_status = signal and signal > 0 and "Connected" or "Weak/No Signal"
        end
    end
    return network_status
end

-- Simplified voltage monitoring (sensor data only)
local function monitorVoltage()
    local new_voltage = tonumber(readVoltage()) or 0
    if new_voltage == 0 then return end

    local voltage_diff = math.abs(new_voltage - last_voltage)
    if last_voltage > 0 and voltage_diff >= (vars.voltage_threshold or 0.5) and vars.voltage_notify_flag then
        sys.taskInit(function()
            if mqtt_module.is_ready() then
                publishSensorData()
            end
        end)
    end
    last_voltage = new_voltage
end

local command_voltage_monitors = {}
local function startCommandVoltageMonitoring(command_type, initial_voltage)
    if command_voltage_monitors[command_type] then return end
    command_voltage_monitors[command_type] = {active = true}

    sys.taskInit(function()
        for i = 1, 30 do
            sys.wait(2000)
            local current_voltage = readVoltage()
            if math.abs(current_voltage - initial_voltage) >= 0.5 then
                if mqtt_module.is_ready() then
                    local temp_hum = readSHTC3()
                    mqtt_module.publish(formatSensorData(temp_hum.temperature, temp_hum.humidity, current_voltage, getGPSPosition(), getRSSI()))
                end
                break
            end
        end
        command_voltage_monitors[command_type] = nil
    end)
end

-- GPS Tracking Functions
local function parseTimeString(time_str)
    -- Parse time strings like "1minut", "30second", "2hour", etc.
    if not time_str then return nil end

    local number, unit = time_str:match("(%d+)(%a+)")
    if not number or not unit then return nil end

    number = tonumber(number)
    if not number or number <= 0 then return nil end

    unit = unit:lower()
    if unit:match("^minut") then
        return number  -- minutes
    elseif unit:match("^second") then
        return math.max(1, math.floor(number / 60))  -- convert to minutes, minimum 1
    elseif unit:match("^hour") then
        return number * 60  -- convert to minutes
    end

    return nil
end

local function autoPublishGPSTracking()
    if not vars.tracking_enabled or not mqtt_module.is_ready() then
        return
    end

    local current_time = os.time()
    if current_time - vars.last_tracking_publish_time < 30 then
        return  -- Rate limit: minimum 30 seconds between publishes
    end

    local voltage = readVoltage()
    if not voltage or voltage <= 13.5 then
        print("GPS tracking: Voltage too low (" .. (voltage or "N/A") .. "V), skipping publish")
        return
    end

    local gps = getGPSPosition()
    if not gps or not gps.speed then
        print("GPS tracking: No GPS data or speed available")
        return
    end

    local speed_kmh = tonumber(gps.speed) or 0
    if speed_kmh <= 5 then
        print("GPS tracking: Speed too low (" .. speed_kmh .. " km/h), skipping publish")
        return
    end

    vars.last_tracking_publish_time = current_time

    sys.taskInit(function()
        local temp_hum = readSHTC3()
        local json_data = formatSensorData(temp_hum.temperature, temp_hum.humidity, voltage, gps, getRSSI())
        local success = mqtt_module.publish(json_data)
        if success then
            print("GPS tracking published: Speed=" .. speed_kmh .. "km/h, Voltage=" .. string.format("%.2f", voltage) .. "V")
        else
            print("GPS tracking publish failed")
        end
    end)
end

local function startTrackingTimer()
    if vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = nil
    end

    if vars.tracking_enabled and vars.tracking_interval_minutes > 0 then
        local interval_ms = vars.tracking_interval_minutes * 60 * 1000
        vars.tracking_timer_id = sys.timerLoopStart(autoPublishGPSTracking, interval_ms)
        print("GPS tracking timer started: " .. vars.tracking_interval_minutes .. " minutes interval")
    end
end

local function stopTrackingTimer()
    if vars.tracking_timer_id then
        sys.timerStop(vars.tracking_timer_id)
        vars.tracking_timer_id = nil
        print("GPS tracking timer stopped")
    end
end

-- Simplified OTA functions
local function update_cb(result)
    ota_in_progress = false
    print("OTA update " .. (result and "success" or "failed"))
    if result and rtos and rtos.reboot then
        sys.taskInit(function() sys.wait(2000); rtos.reboot() end)
    end
end

local function startOTAUpdate()
    if ota_in_progress or not update_available then return false end
    print("Starting OTA update...")
    ota_in_progress = true
    sys.taskInit(function()
        sys.waitUntil("IP_READY", 30000)
        update.request(update_cb)
    end)
    return true
end

-- Consolidated command execution function
local function executeCommand(cmd, publish_data)
    if cmd == "check" then
        commands.checkCommand()
    elseif cmd == "lock" then
        commands.lockCommand()
    elseif cmd == "unlock" then
        commands.unlockCommand()
    elseif cmd == "as" then
        if vars.isLicensed then
            local initial_voltage = readVoltage()
            commands.asCommand()
            startCommandVoltageMonitoring("as", initial_voltage)
        end
    elseif cmd == "untar" then
        local initial_voltage = readVoltage()
        commands.untarCommand()
        startCommandVoltageMonitoring("untar", initial_voltage)
    end

    if publish_data then publishSensorData() end
end

-- Consolidated voltage command handler
local function handleVoltageCommand(command, is_mqtt)
    if command:sub(1, 4) == "volt" then
        local offset = tonumber(command:sub(5))
        if offset then
            vars.voltage_offset = offset
            my_utils.saveConfig("voltage_offset", vars.voltage_offset)
            local msg = string.format("Voltage offset set to %.2f", offset)
            print(msg)
            if is_mqtt and mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"' .. msg .. '"}')
            end
            return msg
        end
    elseif command:sub(1, 2) == "th" then
        local threshold = tonumber(command:sub(3))
        if threshold and threshold > 0 then
            vars.voltage_threshold = threshold
            my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
            local msg = string.format("Voltage threshold set to %.2f", threshold)
            print(msg)
            if is_mqtt and mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"' .. msg .. '"}')
            end
            return msg
        end
    elseif command == "volt status" then
        local voltage = readVoltage()
        local msg = string.format("Volt: %.2fV | Offset: %.2f | Threshold: %.2f | Notify: %s",
            voltage, vars.voltage_offset or 0, vars.voltage_threshold or 0.5,
            vars.voltage_notify_flag and "ON" or "OFF")
        print(msg)
        if is_mqtt and mqtt_module.is_ready() then
            mqtt_module.publish('{"status":"' .. msg .. '"}')
        end
        return msg
    end
    return nil
end

-- Message deduplication and rate limiting
local last_command_time = {}
local last_command_payload = {}

local function handleMQTTMessage(topic, payload)
    local success, data = pcall(json.decode, payload)
    if success and data and data.command then
        local current_time = os.time()
        local command_key = data.command .. (data.id or "")

        -- Check for duplicate messages within 5 seconds
        if last_command_payload[command_key] == payload and
           last_command_time[command_key] and
           (current_time - last_command_time[command_key]) < 5 then
            print("Duplicate cmd")
            return
        end

        -- Update tracking
        last_command_time[command_key] = current_time
        last_command_payload[command_key] = payload

        playBeep("check")

        -- Handle basic commands
        if data.command == "check" or data.command == "lock" or data.command == "unlock" or
           data.command == "as" or data.command == "untar" then
            executeCommand(data.command, true)
        elseif data.command == "update" then
            startOTAUpdate()
        elseif data.command == "restart" then
            print("Restart command received via MQTT")
            sys.taskInit(function()
                sys.wait(2000)
                if rtos and rtos.reboot then
                    rtos.reboot()
                end
            end)
        elseif data.command == "ota_debug" then
            print("OTA: PROJECT=" .. PROJECT .. " VERSION=" .. VERSION)
            if update and update.test then update.test() end
        elseif data.command == "geely_atlas_on" or data.command == "geely_atlas on" then
            -- Enable Geely Atlas mode
            commands.enableGeelyMode()
            print("Geely Atlas mode enabled via MQTT")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Geely Atlas mode enabled"}')
            end
        elseif data.command == "geely_atlas_off" or data.command == "geely_atlas off" then
            -- Disable Geely Atlas mode
            commands.disableGeelyMode()
            print("Geely Atlas mode disabled via MQTT")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Geely Atlas mode disabled"}')
            end
        elseif data.command == "geely_atlas_status" or data.command == "geely_atlas status" then
            -- Get Geely Atlas mode status
            local status = commands.getGeelyModeStatus() and "enabled" or "disabled"
            print("Geely Atlas mode:", status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"geely_atlas_status":"' .. status .. '"}')
            end
        elseif data.command == "geely_status" then
            -- Get Geely Atlas mode status
            local status = commands.getGeelyModeStatus()
            print("Geely Atlas mode status:", status and "enabled" or "disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish(string.format('{"geely_atlas_mode":%s}', status and "true" or "false"))
            end
        elseif data.command == "set_timing" then
            -- Set timing parameter: {"command":"set_timing","parameter":"lock_press","value":1500}
            local param = data.parameter
            local value = tonumber(data.value)
            if param and value and value > 0 then
                local success = commands.setTimingParameter(param, value)
                if success then
                    print(string.format("Timing parameter %s set to %d ms", param, value))
                else
                    print("Failed to set timing parameter:", param)
                end
            else
                print("Invalid timing parameter or value")
            end
        elseif data.command == "get_timing" then
            -- Get all timing parameters
            local params = commands.getTimingParameters()
            print("Current timing parameters:")
            for k, v in pairs(params) do
                print("  " .. k .. ": " .. v .. "ms")
            end
            if mqtt_module.is_ready() then
                local json_params = json.encode(params)
                mqtt_module.publish('{"timing_parameters":' .. json_params .. '}')
            end

        elseif data.command == "gpiotest" then
            sys.taskInit(function()
                local relays = {"Relay1", "Relay2", "Relay3", "KeyPower", "Key1", "Key2"}
                for _, relay in ipairs(relays) do
                    PinModule.relayControl(relay, 1); sys.wait(1000)
                    PinModule.relayControl(relay, 0); sys.wait(200)
                end
                playBeep("check")
            end)
        -- Configuration commands
        elseif data.command == "notify_on" or data.command == "notify_off" then
            vars.voltage_notify_flag = (data.command == "notify_on")
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            local status = vars.voltage_notify_flag and "enabled" or "disabled"
            print("Voltage notifications " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notifications ' .. status .. '"}')
            end
        elseif data.command == "sound_on" or data.command == "sound_off" or
               data.command == "key on" or data.command == "key off" then
            vars.key_state = (data.command == "sound_on" or data.command == "key on")
            my_utils.saveConfig("key_state", vars.key_state)
            local status = vars.key_state and "enabled" or "disabled"
            print("Key state " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Key state ' .. status .. '"}')
            end
        elseif data.command == "key status" or data.command == "key_status" then
            local status = vars.key_state and "enabled" or "disabled"
            print("Key state:", status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"key_status":"' .. status .. '"}')
            end
        elseif data.command == "gps on" or data.command == "gps off" then
            vars.gps_flag = (data.command == "gps on")
            my_utils.saveConfig("gps_flag", vars.gps_flag)
            local status = vars.gps_flag and "enabled" or "disabled"
            print("GPS " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"GPS ' .. status .. '"}')
            end
        elseif data.command == "gps status" or data.command == "gps_status" then
            local status = vars.gps_flag and "enabled" or "disabled"
            print("GPS status:", status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"gps_status":"' .. status .. '"}')
            end
        elseif data.command == "auto_shutdown_on" or data.command == "auto_shutdown_off" then
            vars.auto_shutdown_enabled = (data.command == "auto_shutdown_on")
            my_utils.saveConfig("auto_shutdown_enabled", vars.auto_shutdown_enabled)
            local status = vars.auto_shutdown_enabled and "enabled" or "disabled"
            print("Auto-shutdown " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Auto-shutdown ' .. status .. '"}')
            end
        elseif data.command == "auto_shutdown_minutes" then
            local minutes = tonumber(data.value)
            if minutes and minutes > 0 and minutes <= 120 then
                vars.auto_shutdown_minutes = minutes
                vars.auto_shutdown_time = minutes * 60 * 1000
                my_utils.saveConfig("auto_shutdown_minutes", vars.auto_shutdown_minutes)
                print(string.format("Auto-shutdown time set to %d minutes", minutes))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Auto-shutdown time set to %d minutes"}', minutes))
                end
            else
                print("Invalid auto-shutdown minutes (1-120)")
            end
        elseif data.command == "license" or data.command == "license_disable" then
            vars.isLicensed = false
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_disabled"}')
            end
            -- Send SMS notification to first authorized number
            if vars.phone_number1 and sms then
                local phone_full = "976" .. vars.phone_number1  -- Add country code
                sendSms(phone_full, "License disabled! Vehicle commands blocked.")
            end
        elseif data.command == "charged" or data.command == "license_enable" then
            vars.isLicensed = true
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License enabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_enabled"}')
            end
            -- Send SMS notification to first authorized number
            if vars.phone_number1 and sms then
                local phone_full = "976" .. vars.phone_number1  -- Add country code
                sendSms(phone_full, "License enabled! Vehicle commands active.")
            end
        elseif data.command == "license_status" then
            -- Get license status (Air720 compatible)
            local status = vars.isLicensed and "licensed" or "not_licensed"
            print("License status:", status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"license_status":"' .. status .. '"}')
            end
        elseif data.command == "volt_offset" then
            -- Set voltage offset (Air720 compatible JSON response)
            local offset = tonumber(data.value)
            if offset then
                vars.voltage_offset = offset
                my_utils.saveConfig("voltage_offset", vars.voltage_offset)
                print(string.format("Voltage offset set to %.2f", offset))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            else
                print("Invalid voltage offset value")
            end
        elseif data.command == "volt_threshold" then
            -- Set voltage threshold (Air720 compatible JSON response)
            local threshold = tonumber(data.value)
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
                print(string.format("Voltage threshold set to %.2f", threshold))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            else
                print("Invalid voltage threshold value")
            end
        elseif data.command:sub(1, 4) == "volt" then
            local offset = tonumber(data.command:sub(5))
            if offset then
                vars.voltage_offset = offset
                my_utils.saveConfig("voltage_offset", vars.voltage_offset)
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            end
        elseif data.command:sub(1, 2) == "th" then
            local threshold = tonumber(data.command:sub(3))
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                my_utils.saveConfig("voltage_threshold", vars.voltage_threshold)
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            end
        elseif data.command and data.command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
            local phoneNumber, unitAmount = data.command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
            unitAmount = tonumber(unitAmount)
            if unitAmount and unitAmount <= 2000 and sms and sms.send then
                local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish(string.format('{"status":"Success","message":"Sent Unitel command for %s with %s units"}', phoneNumber, unitAmount))
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send Unitel command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"Invalid Unitel command"}')
            end
        elseif data.command == "sim" then
            -- Handle: {"id":"868909079042886","command":"sim"}
            if sms and sms.send then
                local success = sms.send("1411", "see")
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish('{"status":"Success","message":"Sent SIM balance check command"}')
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send SIM command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"SMS not available"}')
            end
        elseif data.command and data.command:match("^set_server ") then
            -- Handle: {"id":"868909079042886","command":"set_server api.elec.mn"}
            local domain = data.command:match("^set_server (.+)$")
            if domain then
                local success, message = mqtt_module.set_server(domain)
                print("MQTT server change:", message)
                -- Response will be sent before restart in mqtt_module.set_server
            else
                print("Invalid set_server command format")
                if mqtt_module.is_ready() then
                    mqtt_module.publish('{"status":"Error","message":"Invalid format"}')
                end
            end
        elseif data.command == "get_mqtt_server" then
            -- Get current MQTT server
            local current_server = mqtt_module.get_server()
            print("Current MQTT server:", current_server, "Default fallback: aslaa.mn")
            if mqtt_module.is_ready() then
                mqtt_module.publish(string.format('{"status":"Success","server":"%s","default_fallback":"aslaa.mn"}',
                    current_server))
            end
        elseif data.command == "track on" or data.command == "track_on" then
            -- Enable GPS tracking
            vars.tracking_enabled = true
            my_utils.saveConfig("tracking_enabled", vars.tracking_enabled)
            startTrackingTimer()
            print("GPS tracking enabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"GPS tracking enabled"}')
            end
        elseif data.command == "track off" or data.command == "track_off" then
            -- Disable GPS tracking
            vars.tracking_enabled = false
            my_utils.saveConfig("tracking_enabled", vars.tracking_enabled)
            stopTrackingTimer()
            print("GPS tracking disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"GPS tracking disabled"}')
            end
        elseif data.command == "track status" or data.command == "track_status" then
            -- Get GPS tracking status
            local status = vars.tracking_enabled and "enabled" or "disabled"
            local interval = vars.tracking_interval_minutes or 5
            print("GPS tracking:", status, "| Interval:", interval, "minutes")
            if mqtt_module.is_ready() then
                mqtt_module.publish(string.format('{"tracking_status":"%s","interval_minutes":%d}', status, interval))
            end
        elseif data.command and data.command:match("^track timer ") then
            -- Set GPS tracking timer: "track timer 10minut" or "track timer 30second"
            local time_str = data.command:match("^track timer (.+)$")
            local minutes = parseTimeString(time_str)
            if minutes and minutes >= 1 and minutes <= 120 then
                vars.tracking_interval_minutes = minutes
                my_utils.saveConfig("tracking_interval_minutes", vars.tracking_interval_minutes)
                if vars.tracking_enabled then
                    startTrackingTimer()  -- Restart timer with new interval
                end
                print(string.format("GPS tracking timer set to %d minutes", minutes))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"GPS tracking timer set to %d minutes"}', minutes))
                end
            else
                print("Invalid tracking timer (1-120 minutes)")
                if mqtt_module.is_ready() then
                    mqtt_module.publish('{"status":"Invalid tracking timer format or range (1-120 minutes)"}')
                end
            end

        end
    else
        -- Handle Air720-style string commands (non-JSON) - convert to unified format
        local command = payload:lower()
        playBeep("check")

        -- Convert string commands to unified format
        local cmd_map = {
            ["notify on"] = "notify_on", ["notify off"] = "notify_off",
            ["sound on"] = "sound_on", ["sound off"] = "sound_off",
            ["license_enable"] = "license_enable", ["license_disable"] = "license_disable",
            ["track on"] = "track_on", ["track off"] = "track_off"
        }

        local unified_cmd = cmd_map[command] or command

        -- Handle basic commands
        if unified_cmd == "check" or unified_cmd == "lock" or unified_cmd == "unlock" or
           unified_cmd == "as" or unified_cmd == "untar" then
            executeCommand(unified_cmd, unified_cmd == "check")
        -- Handle configuration commands using same logic as JSON
        elseif unified_cmd == "notify_on" or unified_cmd == "notify_off" then
            vars.voltage_notify_flag = (unified_cmd == "notify_on")
            my_utils.saveConfig("voltage_notify_flag", vars.voltage_notify_flag)
            local status = vars.voltage_notify_flag and "enabled" or "disabled"
            print("Voltage notifications " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notifications ' .. status .. '"}')
            end
        elseif unified_cmd == "sound_on" or unified_cmd == "sound_off" or
               unified_cmd == "key_on" or unified_cmd == "key_off" then
            vars.key_state = (unified_cmd == "sound_on" or unified_cmd == "key_on")
            my_utils.saveConfig("key_state", vars.key_state)
            local status = vars.key_state and "enabled" or "disabled"
            print("Key state " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Key state ' .. status .. '"}')
            end
        elseif unified_cmd == "gps_on" or unified_cmd == "gps_off" then
            vars.gps_flag = (unified_cmd == "gps_on")
            my_utils.saveConfig("gps_flag", vars.gps_flag)
            local status = vars.gps_flag and "enabled" or "disabled"
            print("GPS " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"GPS ' .. status .. '"}')
            end
        elseif unified_cmd == "track_on" or unified_cmd == "track_off" then
            vars.tracking_enabled = (unified_cmd == "track_on")
            my_utils.saveConfig("tracking_enabled", vars.tracking_enabled)
            if vars.tracking_enabled then
                startTrackingTimer()
            else
                stopTrackingTimer()
            end
            local status = vars.tracking_enabled and "enabled" or "disabled"
            print("GPS tracking " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"GPS tracking ' .. status .. '"}')
            end
        elseif unified_cmd == "auto_shutdown_on" or unified_cmd == "auto_shutdown_off" then
            vars.auto_shutdown_enabled = (unified_cmd == "auto_shutdown_on")
            my_utils.saveConfig("auto_shutdown_enabled", vars.auto_shutdown_enabled)
            local status = vars.auto_shutdown_enabled and "enabled" or "disabled"
            print("Auto-shutdown " .. status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Auto-shutdown ' .. status .. '"}')
            end
        elseif unified_cmd == "license" or unified_cmd == "license_disable" then
            vars.isLicensed = false
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_disabled"}')
            end
            -- Send SMS notification to first authorized number
            if vars.phone_number1 and sms then
                local phone_full = "976" .. vars.phone_number1  -- Add country code
                sendSms(phone_full, "License disabled! Vehicle commands blocked.")
            end
        elseif unified_cmd == "charged" or unified_cmd == "license_enable" then
            vars.isLicensed = true
            my_utils.saveConfig("isLicensed", vars.isLicensed)
            print("License enabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"license_enabled"}')
            end
            -- Send SMS notification to first authorized number
            if vars.phone_number1 and sms then
                local phone_full = "976" .. vars.phone_number1  -- Add country code
                sendSms(phone_full, "License enabled! Vehicle commands active.")
            end
        elseif unified_cmd == "license_status" then
            local status = vars.isLicensed and "licensed" or "not_licensed"
            print("License status:", status)
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"license_status":"' .. status .. '"}')
            end
        elseif command:sub(1, 4) == "volt" or command:sub(1, 2) == "th" or command == "volt status" then
            -- Handle voltage commands
            handleVoltageCommand(command, true)
        elseif command:match("^track timer ") then
            -- Handle GPS tracking timer: "track timer 10minut"
            local time_str = command:match("^track timer (.+)$")
            local minutes = parseTimeString(time_str)
            if minutes and minutes >= 1 and minutes <= 120 then
                vars.tracking_interval_minutes = minutes
                my_utils.saveConfig("tracking_interval_minutes", vars.tracking_interval_minutes)
                if vars.tracking_enabled then
                    startTrackingTimer()  -- Restart timer with new interval
                end
                print(string.format("GPS tracking timer set to %d minutes", minutes))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"GPS tracking timer set to %d minutes"}', minutes))
                end
            else
                print("Invalid tracking timer (1-120 minutes)")
                if mqtt_module.is_ready() then
                    mqtt_module.publish('{"status":"Invalid tracking timer format or range (1-120 minutes)"}')
                end
            end
        elseif command == "track status" then
            -- Get GPS tracking status
            local status = vars.tracking_enabled and "enabled" or "disabled"
            local interval = vars.tracking_interval_minutes or 5
            print("GPS tracking:", status, "| Interval:", interval, "minutes")
            if mqtt_module.is_ready() then
                mqtt_module.publish(string.format('{"tracking_status":"%s","interval_minutes":%d}', status, interval))
            end
        elseif command == "geely atlas on" then
            -- Enable Geely Atlas mode (Air720 format)
            commands.enableGeelyMode()
            print("Geely Atlas mode enabled via MQTT")
        elseif command == "geely atlas off" then
            -- Disable Geely Atlas mode (Air720 format)
            commands.disableGeelyMode()
            print("Geely Atlas mode disabled via MQTT")
        elseif command == "auto_shutdown on" then
            -- Enable auto-shutdown
            vars.auto_shutdown_enabled = true
            my_utils.saveConfig("auto_shutdown_enabled", vars.auto_shutdown_enabled)
            print("Auto-shutdown enabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Auto-shutdown enabled"}')
            end
        elseif command == "auto_shutdown off" then
            -- Disable auto-shutdown
            vars.auto_shutdown_enabled = false
            my_utils.saveConfig("auto_shutdown_enabled", vars.auto_shutdown_enabled)
            print("Auto-shutdown disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Auto-shutdown disabled"}')
            end
        elseif command:sub(1, 18) == "auto_shutdown timer" then
            -- Set auto-shutdown timer: "auto_shutdown timer 10"
            local minutes = tonumber(command:sub(20))
            if minutes and minutes > 0 and minutes <= 120 then
                vars.auto_shutdown_minutes = minutes
                vars.auto_shutdown_time = minutes * 60 * 1000
                my_utils.saveConfig("auto_shutdown_minutes", vars.auto_shutdown_minutes)
                print(string.format("Auto-shutdown timer set to %d minutes", minutes))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Auto-shutdown timer set to %d minutes"}', minutes))
                end
            else
                print("Invalid auto-shutdown timer (1-120 minutes)")
                if mqtt_module.is_ready() then
                    mqtt_module.publish('{"status":"Invalid auto-shutdown timer (1-120 minutes)"}')
                end
            end
        elseif command:sub(1, 5) == "time:" then
            -- Air720 format: "time:param_name:value_ms"
            -- Example: "time:lock_press:1500"
            local parts = {}
            for part in string.gmatch(command, "[^:]+") do
                table.insert(parts, part)
            end

            if #parts == 3 then
                local param = parts[2]
                local value = tonumber(parts[3])
                if param and value and value > 0 then
                    local success = commands.setTimingParameter(param, value)
                    if success then
                        print(string.format("Timing parameter %s set to %d ms", param, value))
                    else
                        print("Failed to set timing parameter:", param)
                    end
                else
                    print("Invalid timing parameter or value")
                end
            else
                print("Invalid time command format. Use: time:param_name:value_ms")
            end
        elseif command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
            local phoneNumber, unitAmount = command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
            unitAmount = tonumber(unitAmount)
            if unitAmount and unitAmount <= 2000 and sms and sms.send then
                local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish(string.format('{"status":"Success","message":"Sent Unitel command for %s with %s units"}', phoneNumber, unitAmount))
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send Unitel command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"Invalid Unitel command"}')
            end
        elseif command == "sim" then
            -- Handle: sim
            if sms and sms.send then
                local success = sms.send("1411", "see")
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish('{"status":"Success","message":"Sent SIM balance check command"}')
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send SIM command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"SMS not available"}')
            end
        else
            print("Unknown command:", command)
        end
    end
end

-- MQTT initial data publishing handler
local function handleInitialDataPublish()
    publishSensorData()
end

-- Simplified SMS command processing with authorization
local function processSmsCommand(command, callback_number)
    -- Extract last 8 digits for authorization check
    local short_num = string.sub(callback_number, -8)

    -- Check if the number is authorized (compare using short numbers)
    local is_authorized = (short_num == vars.phone_number1 or
                          short_num == vars.phone_number2 or
                          short_num == vars.phone_number3)

    -- Commands that require authorization (Air720 compatible)
    local restricted_commands = {
        ["as"] = true,
        ["untar"] = true,
        ["lock"] = true,
        ["unlock"] = true
    }

    -- Check authorization for restricted commands
    if restricted_commands[command] and not is_authorized then
        sendSms(callback_number, "taniulaagvi dugaar!")  -- Unauthorized number message
        print("Unauthorized SMS")
        return
    end

    -- Check license for restricted commands (only if authorized)
    if not vars.isLicensed and restricted_commands[command] then
        sendSms(callback_number, "License expired")
        return
    end

    local commands_map = {
        check = function()
            local temp_hum = readSHTC3()
            local voltage = readVoltage()
            local gps = getGPSPosition()

            local gpsMessage = (gps and gps.lat and gps.lng and gps.lat ~= 0 and gps.lng ~= 0)
                and string.format("https://maps.google.com/?q=%.6f,%.6f", gps.lat, gps.lng)
                or "GPS signal lost"

            sendSms(callback_number, string.format("Tanii mashin:\n%s\nBatt: %.2fV | Temp: %.1fC\nHum: %.1f%%",
                gpsMessage, voltage, temp_hum.temperature, temp_hum.humidity))
        end,
        lock = function() commands.lockCommand(); sendSms(callback_number, "locked") end,
        unlock = function() commands.unlockCommand(); sendSms(callback_number, "unlocked") end,
        as = function()
            local initial_voltage = readVoltage()
            commands.asCommand()
            startCommandVoltageMonitoring("as", initial_voltage)
            sys.taskInit(function()
                sys.wait(8000)
                local voltage = readVoltage()
                local temp_hum = readSHTC3()
                local status = voltage >= 13.4 and "aslaa" or "tulhuur taniagvi"
                sendSms(callback_number, string.format("Tani mashin %s!\nBatt: %.2fV | Temp: %.1fC", status, voltage, temp_hum.temperature))
            end)
        end,
        untar = function()
            local initial_voltage = readVoltage()
            commands.untarCommand()
            startCommandVoltageMonitoring("untar", initial_voltage)
            sys.taskInit(function()
                sys.wait(10000)
                local voltage = readVoltage()
                local temp_hum = readSHTC3()
                local status = voltage <= 13.5 and "untarlaa" or "check"
                sendSms(callback_number, string.format("Tani mashin:\nBatt: %.2fV | Temp: %.1fC\nStatus: %s", voltage, temp_hum.temperature, status))
            end)
        end
    }

    if commands_map[command] then
        commands_map[command]()
    end
end

-- Simplified initialization
local function initTask()
    print("Air780EG v" .. VERSION)

    PinModule.setupPins()
    initSHTC3(); initADC(); initGPS(); initSMS()
    my_utils.createDirectory("/data")
    my_utils.loadConfiguration()
    initMobile()
    mqtt_module.init()

    sys.subscribe("MQTT_MESSAGE_RECEIVED", handleMQTTMessage)
    sys.subscribe("MQTT_CONNECTED", function() PinModule.setCloudLED(1); playBeep("check") end)
    sys.subscribe("MQTT_DISCONNECTED", function() PinModule.setCloudLED(0) end)

    sys.taskInit(sensorTask)
    sys.timerLoopStart(monitorVoltage, 10000)

    -- Initialize GPS tracking timer if enabled
    if vars.tracking_enabled then
        startTrackingTimer()
    end

    -- Network monitoring
    sys.taskInit(function()
        while true do
            sys.wait(5000)
            if mobile and mobile.status then
                local status = mobile.status()
                PinModule.setNetLED((status == "REGISTERED" or status == "REGISTERED_ROAMING" or
                                   status == 1 or status == 2 or status == 5 or getRSSI() ~= 0) and 1 or 0)
            end
        end
    end)

    -- SMS processing
    sys.taskInit(function()
        while true do
            if vars.sms_data then
                processSmsCommand(vars.sms_data, vars.callback_number)
                vars.sms_data, vars.callback_number = nil, nil
            end
            sys.wait(1000)
        end
    end)

    print("Ready! Auth:", vars.phone_number1 or "none")

    -- UART1 command processing variables
    local debounceTime = 2  -- 2 seconds debounce (vars.currentTime is in seconds)
    local lastLockTime = -debounceTime  -- Initialize to allow first command
    local lastUnlockTime = -debounceTime  -- Initialize to allow first command
    local mirrorPressCount = 0
    local mirrorWindowStartTime = 0
    local mirrorPressWindow = 10000  -- 10 seconds window
    local lastSpecificCommandTime = {}
    local commandCounter = {}
    local triggerCount = 3
    local cleanup_counter = 0  -- For periodic memory cleanup

    -- UART1 processing task
    sys.taskInit(function()
        if uart and uart.setup then
            local result = uart.setup(1, 9600, 8, 1, uart.NONE, uart.LSB, 2048)
            if result == 0 then
                print("UART1 initialized (9600 baud, 2KB buffer)")
            else
                print("UART1 init failed:", result)
            end
        else
            print("UART module not available")
        end

        while true do
            local available_bytes = uart.rxSize(1)
            local uart1_data = ""

            if available_bytes > 0 then
                uart1_data = uart.read(1, math.min(available_bytes, 128)) or ""
            end

            if #uart1_data > 0 then
                local trimmed_data = uart1_data:match("^%s*(.-)%s*$")
                print("Received UART Data: " .. trimmed_data)

                if #trimmed_data >= 9 then
                    local identifier = trimmed_data:sub(9, 9)  -- 9th character - the command identifier
                    print("UART identifier: " .. identifier .. " from: " .. trimmed_data .. " currentTime: " .. vars.currentTime)

                    if identifier == "8" then
                        local timeSinceLastLock = vars.currentTime - lastLockTime
                        print("Lock cmd: time since last=" .. timeSinceLastLock .. " debounce=" .. debounceTime)
                        if timeSinceLastLock > debounceTime then
                            print("Lock command detected and executed")
                            executeCommand("lock", true)
                            lastLockTime = vars.currentTime
                        else
                            print("Lock command debounced")
                        end

                    elseif identifier == "4" then
                        local timeSinceLastUnlock = vars.currentTime - lastUnlockTime
                        print("Unlock cmd: time since last=" .. timeSinceLastUnlock .. " debounce=" .. debounceTime)
                        if timeSinceLastUnlock > debounceTime then
                            print("Unlock command received and executed")
                            executeCommand("unlock", true)
                            lastUnlockTime = vars.currentTime
                        else
                            print("Unlock command debounced")
                        end

                    elseif identifier == "2" then
                        if mirrorPressCount == 0 then
                            mirrorWindowStartTime = vars.currentTime
                        end
                        mirrorPressCount = mirrorPressCount + 1
                        print("Mirror press count: " .. mirrorPressCount)

                        if mirrorPressCount >= 3 then
                            print("Mirror command executed after 3 presses")
                            executeCommand("check", true)
                            mirrorPressCount = 0
                        end

                    elseif identifier == "1" then
                        if not lastSpecificCommandTime[trimmed_data] or
                            (vars.currentTime - lastSpecificCommandTime[trimmed_data] > debounceTime) then
                            commandCounter[trimmed_data] = (commandCounter[trimmed_data] or 0) + 1
                            print("Test cmd count: " .. commandCounter[trimmed_data] .. "/" .. triggerCount)

                            if commandCounter[trimmed_data] >= triggerCount then
                                print("Test command executed when trigger count met")
                                if vars.isLicensed then
                                    executeCommand("as", true)
                                end
                                commandCounter[trimmed_data] = 0
                                lastSpecificCommandTime[trimmed_data] = vars.currentTime
                            end
                        end
                    else
        
                    end
                else

                end
            end

            if mirrorPressCount > 0 and (vars.currentTime - mirrorWindowStartTime > mirrorPressWindow) then

                mirrorPressCount = 0
            end

            cleanup_counter = cleanup_counter + 1
            if cleanup_counter >= 1000 then
                local cutoff_time = vars.currentTime - 3600000
                local cleaned_count = 0
                for key, time in pairs(lastSpecificCommandTime) do
                    if time < cutoff_time then
                        lastSpecificCommandTime[key] = nil
                        commandCounter[key] = nil
                        cleaned_count = cleaned_count + 1
                    end
                end
                if cleaned_count > 0 then
                    print("UART1: Cleaned " .. cleaned_count .. " old command entries")
                end
                cleanup_counter = 0
            end

            sys.wait(200) -- 200ms delay
        end
    end)
end

-- Main application entry point
local function main()
    -- Start the initialization task in a coroutine
    sys.taskInit(initTask)

    -- Keep the system running
    sys.run()
end

-- Start the application
main()
