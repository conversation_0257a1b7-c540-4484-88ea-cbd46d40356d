
local my_utils = {}
local vars = require('variables')

function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

function my_utils.readFile(path)
    local file = io.open(path, "r")
    if file then
        local content = file:read("*a")
        file:close()
        return content
    end
    return nil
end

function my_utils.createDirectory(path)
    -- For Air780EG, use the official io.mkdir method
    print("Creating directory:", path)

    -- Method 1: Use io.mkdir (Air780EG official method)
    if io and io.mkdir then
        local success, err = io.mkdir(path)
        if success then
            print("Directory created with io.mkdir:", path)
            return true
        else
            print("io.mkdir failed:", err or "unknown error")
        end
    end

    -- Method 2: Try rtos.make_dir as fallback
    if rtos and rtos.make_dir then
        local success, err = pcall(rtos.make_dir, path)
        if success then
            print("Directory created with rtos.make_dir:", path)
            return true
        else
            print("rtos.make_dir failed:", err)
        end
    end

    -- Method 3: Try to create a test file to verify directory exists/can be created
    local test_file = io.open(path .. "/test.tmp", "w")
    if test_file then
        test_file:close()
        os.remove(path .. "/test.tmp")
        print("Directory verified with test file:", path)
        return true
    end

    print("All directory creation methods failed for:", path)
    return false
end

function my_utils.writeToFile(path, content)
    print("Attempting to write to file:", path, "Content:", tostring(content))

    -- Ensure directory exists
    local dir = string.match(path, "(.+)/[^/]+$")
    if dir then
        print("Ensuring directory exists:", dir)
        local dir_created = my_utils.createDirectory(dir)
        if not dir_created then
            print("Warning: Directory creation failed, but attempting file write anyway")
        end
    end

    -- Use standard Air780EG io.open approach
    local file = io.open(path, "w")
    if file then
        file:write(tostring(content))
        file:close()
        print("Successfully wrote to file:", path)
        return true
    else
        print("Failed to open file for writing:", path)
        return false
    end
end

function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Load configuration from files
function my_utils.loadConfiguration()
    -- Configuration mapping: {var_name, file_path, default_value, type}
    local config_map = {
        {"phone_number1", "/data/phone1.txt", nil, "string"},
        {"phone_number2", "/data/phone2.txt", nil, "string"},
        {"phone_number3", "/data/phone3.txt", nil, "string"},
        {"voltage_offset", "/data/volt_offset.txt", 0, "number"},
        {"voltage_threshold", "/data/volt_threshold.txt", 0.5, "number"},
        {"voltage_notify_flag", "/data/volt_notify.txt", false, "boolean"},
        {"isLicensed", "/data/license.txt", true, "boolean"},
        {"key_state", "/data/key_state.txt", false, "boolean"},
        {"sound_flag", "/data/sound_flag.txt", true, "boolean"},
        {"gps_flag", "/data/gps_flag.txt", true, "boolean"},
        {"geely_atlas_mode", "/data/geely_mode.txt", false, "boolean"},
        {"lock_wait_duration", "/data/lock_wait.txt", 2000, "number"},
        {"lock_press_duration", "/data/lock_press.txt", 1000, "number"},
        {"unlock_wait_duration", "/data/unlock_wait.txt", 1000, "number"},
        {"unlock_press_duration", "/data/unlock_press.txt", 1000, "number"},
        {"mirror_duration", "/data/mirror_duration.txt", 2000, "number"}
    }

    for _, config in ipairs(config_map) do
        local var_name, file_path, default_value, value_type = config[1], config[2], config[3], config[4]
        if my_utils.fileExists(file_path) then
            local content = my_utils.readFile(file_path)
            if value_type == "number" then
                vars[var_name] = tonumber(content) or default_value
            elseif value_type == "boolean" then
                vars[var_name] = content == "true"
            else
                vars[var_name] = content
            end
        else
            vars[var_name] = default_value
        end
    end
    print("Configuration loaded from persistent storage")
end

-- Save individual configuration settings
function my_utils.saveConfig(key, value)
    local file_map = {
        voltage_offset = "/data/volt_offset.txt",
        voltage_threshold = "/data/volt_threshold.txt",
        voltage_notify_flag = "/data/volt_notify.txt",
        key_state = "/data/key_state.txt",
        sound_flag = "/data/sound_flag.txt",
        gps_flag = "/data/gps_flag.txt",
        geely_atlas_mode = "/data/geely_mode.txt",
        isLicensed = "/data/license.txt",
        lock_wait_duration = "/data/lock_wait.txt",
        lock_press_duration = "/data/lock_press.txt",
        unlock_wait_duration = "/data/unlock_wait.txt",
        unlock_press_duration = "/data/unlock_press.txt",
        mirror_duration = "/data/mirror_duration.txt"
    }

    local file_path = file_map[key]
    if file_path then
        local success = my_utils.writeToFile(file_path, tostring(value))
        if success then
            print("Saved config:", key, "=", value)
        else
            print("Failed to save config:", key)
        end
        return success
    else
        print("Unknown config key:", key)
        return false
    end
end

-- Save all current configuration to files
function my_utils.saveAllConfig()
    local config_keys = {"voltage_offset", "voltage_threshold", "voltage_notify_flag", "key_state",
                        "sound_flag", "gps_flag", "geely_atlas_mode", "isLicensed", "lock_wait_duration",
                        "lock_press_duration", "unlock_wait_duration", "unlock_press_duration", "mirror_duration"}
    for _, key in ipairs(config_keys) do
        my_utils.saveConfig(key, vars[key])
    end
    print("All configuration saved to persistent storage")
end

return my_utils
