local mqtt_module = {}
local sys = require("sys")
local PinModule = require("pin_module")
_G.sysplus = require("sysplus")
if not log then
    _G.log = {
        info = function(tag, ...) print("[" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN]", ...) end,
        error = function(tag, ...) print("[ERROR]", ...) end,
        debug = function() end
    }
end

local mqtt_server = "aslaa.mn"
local mqtt_port = 1883
local mqtt_username = "admin"
local mqtt_password = "public"
local mqtt_clientid = nil
local default_server = "aslaa.mn"
local fallback_server = "aslaa.mn"
local connection_attempts = 0
local max_attempts = 2
local retry_count = 0

local mqtt_client = nil
local mqtt_ready = false
local mqtt_payload = nil
local retry_count = 0
local fallback_server = nil
local connection_attempts = 0
local max_connection_attempts = 3
local function getDeviceId()
    if mobile and mobile.imei then
        local imei = mobile.imei()
        if imei and imei ~= "" then return imei end
    end
    return "air780e_" .. os.time()
end

-- MQTT connection task
local function mqtt_task()
    log.info("MQTT", "Starting MQTT task")

    -- Wait for network to be ready
    sys.waitUntil("IP_READY", 30000)

    while true do
        mqtt_ready = false

        -- Make sure we have a client ID
        if mqtt_clientid == nil then
            mqtt_clientid = getDeviceId()
        end

        log.info("MQTT", "Connecting to MQTT server", mqtt_server, "port", mqtt_port)
        log.info("MQTT", "Using client ID:", mqtt_clientid)

        -- Beep to indicate MQTT connection attempt (more audible)
        sys.taskInit(function()
            PinModule.beepPattern(3, 200, 300, 2800)  -- 3 longer beeps at 2.8kHz with longer pauses
            sys.wait(500)  -- Wait before proceeding to connection
        end)

        -- Create MQTT client (following official documentation pattern)
        mqtt_client = mqtt.create(nil, mqtt_server, mqtt_port, false, false)

        if not mqtt_client then
            log.error("MQTT", "Failed to create MQTT client")
            connection_attempts = connection_attempts + 1

            -- Check if we should fallback to default server
            if connection_attempts >= max_attempts and mqtt_server ~= default_server then
                log.warn("MQTT", "Max attempts reached, falling back to default server:", default_server)
                mqtt_server = default_server
                connection_attempts = 0
                retry_count = 0  -- Reset retry count for new server
                -- Save fallback to persistent storage
                local my_utils = require("my_utils")
                my_utils.writeToFile("/data/mqtt_server.txt", default_server)
                sys.wait(2000)  -- Short wait before trying fallback server
            else
                -- Wait before retrying (shorter wait for fallback attempts)
                local wait_time = connection_attempts < max_attempts and 5 or 10
                log.info("MQTT", "Waiting", wait_time, "seconds before retrying...")
                sys.wait(wait_time * 1000)
            end
            goto continue
        end

        -- Set up authentication
        mqtt_client:auth(mqtt_clientid, mqtt_username, mqtt_password)

        -- Set keep-alive interval (240 seconds as recommended by docs)
        mqtt_client:keepalive(240)

        -- Enable auto-reconnect with 3 second interval
        mqtt_client:autoreconn(true, 3000)

        -- Set up MQTT callback
        mqtt_client:on(function(client, event, data, payload)
            if event == "conack" then
                -- Connected and authenticated
                log.info("MQTT", "Connected to MQTT server:", mqtt_server)
                mqtt_ready = true
                retry_count = 0
                connection_attempts = 0  -- Reset connection attempts on success

                -- Publish the MQTT_CONNECTED event
                sys.publish("MQTT_CONNECTED")

                -- Subscribe and publish initial data
                sys.timerStart(function()
                    if mqtt_client and mqtt_client:ready() then
                        -- Subscribe with QoS 1 for better reliability
                        mqtt_client:subscribe(mqtt_clientid, 1)
                        -- Publish with QoS 1 for better reliability
                        mqtt_client:publish(mqtt_clientid .. "/msg", string.format('{"server":"%s"}', mqtt_server), 1, 0)
                        sys.publish("MQTT_PUBLISH_INITIAL_DATA")
                    end
                end, 1000)

            elseif event == "recv" then
                -- Received message
                log.info("MQTT", "Received message on topic:", data, "payload:", payload)
                mqtt_payload = payload
                sys.publish("MQTT_MESSAGE_RECEIVED", data, payload)

            elseif event == "sent" then
                -- Message sent successfully
                log.info("MQTT", "Message sent, ID:", data)
                sys.publish("MQTT_SENT", data)

            elseif event == "disconnect" then
                -- Disconnected from server
                log.warn("MQTT", "Disconnected from MQTT server")
                mqtt_ready = false

                -- Publish the MQTT_DISCONNECTED event
                sys.publish("MQTT_DISCONNECTED")

                -- Auto-reconnect is enabled, so we don't need manual reconnection logic
                -- The client will handle reconnection automatically

            elseif event == "pong" then
                -- Received ping response
                log.debug("MQTT", "Received MQTT ping response")
            end
        end)

        -- Wait a moment for the connection attempt beeps to finish
        sys.wait(1000)

        -- Connect to MQTT server
        log.info("MQTT", "Connecting to MQTT server...")

        -- Beep to indicate actual connection attempt (more distinct)
        sys.taskInit(function()
            PinModule.beep(1500, 400)  -- Single lower-pitched longer beep at 1.5kHz for 400ms
        end)

        -- Connect to MQTT server (auto-reconnect will handle failures)
        mqtt_client:connect()

        -- Wait for connection to be established or failed
        sys.waitUntil("MQTT_CONNECTED", 30000)

        if mqtt_ready then
            -- Connection successful, wait for disconnect
            sys.waitUntil("MQTT_DISCONNECTED")
        else
            -- Connection failed, try fallback server if needed
            connection_attempts = connection_attempts + 1
            if connection_attempts >= max_attempts and mqtt_server ~= default_server then
                log.warn("MQTT", "Max attempts reached, falling back to default server:", default_server)
                mqtt_server = default_server
                connection_attempts = 0
                -- Save fallback to persistent storage
                local my_utils = require("my_utils")
                my_utils.writeToFile("/data/mqtt_server.txt", default_server)
            end
        end

        -- Close the client before retrying
        if mqtt_client then
            mqtt_client:close()
            mqtt_client = nil
        end

        -- Wait before retrying
        sys.wait(5000)

        ::continue::
    end
end

-- Initialize the module
function mqtt_module.init()
    -- Load saved server configuration
    mqtt_module.load_server_config()

    -- Start the MQTT task
    sys.taskInit(mqtt_task)
    return true
end

-- Publish a message to a topic
function mqtt_module.publish(data, topic)
    -- If topic is not provided, use the default topic (IMEI/msg)
    if not topic then
        topic = mqtt_clientid .. "/msg"
    end

    -- Check if client is ready
    if not mqtt_client or not mqtt_ready then
        log.warn("MQTT", "Client not ready, cannot publish")
        return false
    end

    -- Publish the message with QoS 1 for better reliability
    local success, result = pcall(function()
        return mqtt_client:publish(topic, data, 1, 0)
    end)

    if success and result then
        log.info("MQTT", "Published message to topic:", topic)
        return true
    else
        log.error("MQTT", "Failed to publish message:", result or "unknown error")
        return false
    end
end

-- Get the last received message
function mqtt_module.get_last_message()
    return mqtt_payload
end

-- Clear the last received message
function mqtt_module.clear_message()
    mqtt_payload = nil
end

-- Check if MQTT client is ready
function mqtt_module.is_ready()
    return mqtt_ready
end

-- Get the client ID (IMEI)
function mqtt_module.get_client_id()
    return mqtt_clientid
end

-- Get the default publish topic (IMEI/msg)
function mqtt_module.get_publish_topic()
    return mqtt_clientid .. "/msg"
end

-- Get the default subscribe topic (IMEI)
function mqtt_module.get_subscribe_topic()
    return mqtt_clientid
end

-- Set server without automatic prefix - save exactly as provided
function mqtt_module.set_server(domain)
    if not domain or domain == "" then
        return false, "Invalid server domain"
    end

    -- Use the domain exactly as provided
    mqtt_server = domain
    fallback_server = domain  -- Same domain as fallback
    connection_attempts = 0

    -- Save both servers to persistent storage
    local my_utils = require("my_utils")
    local success1 = my_utils.writeToFile("/data/mqtt_server.txt", domain)
    local success2 = my_utils.writeToFile("/data/mqtt_fallback.txt", domain)

    if success1 and success2 then
        log.info("MQTT", "Server set to:", domain, "with fallback:", domain)

        -- Send confirmation message and restart in a task
        sys.taskInit(function()
            -- Send confirmation message before restart
            if mqtt_client and mqtt_ready then
                local response = string.format('{"status":"Success","message":"Server set to %s. Restarting..."}', domain)
                mqtt_client:publish(mqtt_clientid .. "/msg", response, 0, 0)
                sys.wait(1000)  -- Wait for message to be sent
            end

            -- Restart device to connect to new server
            log.info("MQTT", "Restarting device to connect to new server...")
            sys.wait(2000)  -- Wait 2 seconds before restart
            if rtos and rtos.reboot then
                rtos.reboot()
            end
        end)

        return true, "Server set to: " .. domain .. ". Restarting..."
    else
        return false, "Failed to save server configuration"
    end
end

-- Get current MQTT server
function mqtt_module.get_server()
    return mqtt_server
end

-- Get fallback server
function mqtt_module.get_fallback_server()
    return fallback_server
end

-- Load MQTT server from persistent storage
function mqtt_module.load_server_config()
    local my_utils = require("my_utils")

    -- Load saved server
    if my_utils.fileExists("/data/mqtt_server.txt") then
        local saved_server = my_utils.readFile("/data/mqtt_server.txt")
        if saved_server and saved_server ~= "" then
            mqtt_server = saved_server
            fallback_server = saved_server  -- Same as primary
            log.info("MQTT", "Loaded saved server:", mqtt_server)
        end
    end
end

-- Return the module
return mqtt_module
